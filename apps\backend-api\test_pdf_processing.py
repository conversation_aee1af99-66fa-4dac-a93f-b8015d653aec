#!/usr/bin/env python3
"""
Test script för att verifiera PDF-bearbetning med nya ändringar
"""

import base64
import tempfile
import os
from app.services.ocr_service import OCRService

def create_test_pdf_with_text():
    """Skapa en enkel PDF med text för testning"""
    pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj
4 0 obj
<<
/Length 100
>>
stream
BT
/F1 12 Tf
100 700 Td
(FAKTURA) Tj
0 -20 Td
(Fakturanummer: 12345) Tj
0 -20 Td
(Datum: 2024-01-15) Tj
0 -20 Td
(Belopp: 1500.00 SEK) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000074 00000 n 
0000000120 00000 n 
0000000179 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF"""
    return pdf_content

def create_test_pdf_without_text():
    """Skapa en PDF utan text (simulerar skannad bild)"""
    pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
>>
endobj
xref
0 4
0000000000 65535 f 
0000000009 00000 n 
0000000074 00000 n 
0000000120 00000 n 
trailer
<<
/Size 4
/Root 1 0 R
>>
startxref
170
%%EOF"""
    return pdf_content

def test_pdf_with_text():
    """Testa PDF med extraherad text"""
    print("=== Test: PDF med text ===")
    
    pdf_content = create_test_pdf_with_text()
    pdf_base64 = base64.b64encode(pdf_content).decode('utf-8')
    
    ocr_service = OCRService()
    
    try:
        text_content, image_data = ocr_service.process_file_for_llm(pdf_base64, 'pdf')
        
        print(f"Text content: {text_content}")
        print(f"Image data: {'Yes' if image_data else 'No'}")
        
        if text_content and not image_data:
            print("✅ SUCCESS: PDF med text behandlades korrekt som text")
        else:
            print("❌ FAIL: PDF med text behandlades inte korrekt")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")

def test_pdf_without_text():
    """Testa PDF utan text (ska konverteras till bild)"""
    print("\n=== Test: PDF utan text ===")
    
    pdf_content = create_test_pdf_without_text()
    pdf_base64 = base64.b64encode(pdf_content).decode('utf-8')
    
    ocr_service = OCRService()
    
    try:
        text_content, image_data = ocr_service.process_file_for_llm(pdf_base64, 'pdf')
        
        print(f"Text content: {text_content}")
        print(f"Image data: {'Yes' if image_data else 'No'}")
        print(f"Image data length: {len(image_data) if image_data else 0} chars")
        
        if not text_content and image_data:
            print("✅ SUCCESS: PDF utan text konverterades till bild")
        else:
            print("❌ FAIL: PDF utan text behandlades inte korrekt")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")

def test_regular_image():
    """Testa vanlig bild (ska behandlas som bild)"""
    print("\n=== Test: Vanlig bild ===")
    
    # Skapa en enkel 1x1 pixel PNG
    png_content = base64.b64decode(
        "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA4nEKtAAAAABJRU5ErkJggg=="
    )
    png_base64 = base64.b64encode(png_content).decode('utf-8')
    
    ocr_service = OCRService()
    
    try:
        text_content, image_data = ocr_service.process_file_for_llm(png_base64, 'png')
        
        print(f"Text content: {text_content}")
        print(f"Image data: {'Yes' if image_data else 'No'}")
        
        if not text_content and image_data:
            print("✅ SUCCESS: Vanlig bild behandlades korrekt som bild")
        else:
            print("❌ FAIL: Vanlig bild behandlades inte korrekt")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    print("Testar PDF-bearbetning med nya ändringar...")
    
    test_pdf_with_text()
    test_pdf_without_text()
    test_regular_image()
    
    print("\n=== Test slutfört ===")
