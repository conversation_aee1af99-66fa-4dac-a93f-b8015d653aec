# Installera Poppler på Windows

För att få full funktionalitet med PDF-till-bild-konvertering behöver du installera Poppler.

## Snabb installation

1. **Ladda ner Poppler:**
   - Gå till: https://github.com/oschwartz10612/poppler-windows/releases/
   - Ladda ner senaste `Release-XX.XX.X-0.zip`

2. **Extrahera:**
   - Extrahera till `C:\poppler` (el<PERSON> annan plats)

3. **L<PERSON>gg till PATH:**
   - Öppna "Environment Variables" (Miljövariabler)
   - <PERSON>ä<PERSON> till `C:\poppler\bin` till system PATH
   - Starta om terminalen

4. **Testa:**
   ```cmd
   pdftoppm -h
   ```
   Om detta fungerar är poppler korrekt installerat.

## Alternativ installation med Chocolatey

Om du har Chocolatey installerat:
```cmd
choco install poppler
```

## Testa PDF-funktionaliteten

Efter installation, kör:
```bash
cd apps/backend-api
python test_pdf_processing.py
```

Du bör nu se att PDF:er utan text konverteras till bilder istället för att ge felmeddelanden.
